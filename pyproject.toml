[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "hass-mcp"
version = "0.2.0"
description = "A Home Assistant tool provider for LLMs using FastMCP"
requires-python = ">=3.13"
dependencies = [
    "fastmcp[cli]>=2.8.1",
    "httpx>=0.27.0",
    "uvicorn>=0.29.0",
    "python-dotenv>=1.0.0"
]

[project.optional-dependencies]
test = [
    "pytest>=8.0.0",
]

[project.scripts]
hass-mcp = "app.__main__:main"