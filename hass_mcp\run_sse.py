#!/usr/bin/env python
"""
SSE-specific entry point for running Hass-MCP with Server-Sent Events transport.
This script demonstrates how to run the server with SSE transport for web-based clients.
"""

import os
import logging
from .server import mcp_server

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """
    Run the Hass-MCP server with SSE transport.
    This enables web-based clients to connect via Server-Sent Events.
    """
    # Configuration from environment variables
    host = os.environ.get("MCP_HOST", "0.0.0.0")
    port = int(os.environ.get("MCP_PORT", "8000"))
    
    logger.info(f"Starting Hass-MCP server with SSE transport on {host}:{port}")
    
    # Run with SSE transport using FastMCP 2.8.1 built-in support
    mcp_server.run(
        transport="sse",
        host=host,
        port=port
    )

if __name__ == "__main__":
    main()
