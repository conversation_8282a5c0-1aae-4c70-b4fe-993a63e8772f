#!/usr/bin/env python
"""Entry point for running Hass-MCP as a module"""

import os

from .server import mcp_server

def main():
    """
    Run the Hass-MCP server using FastMCP 2.8.1 standard approach.
    Supports multiple transport protocols: stdio, sse, streamable-http
    """
    # Get transport mode from environment variable, default to stdio for MCP compatibility
    transport = os.environ.get("MCP_TRANSPORT", "stdio")

    if transport == "sse":
        # Run with SSE transport for web-based clients
        mcp_server.run(transport="sse", host="0.0.0.0", port=8000)
    elif transport == "streamable-http":
        # Run with streamable HTTP transport (modern alternative to SSE)
        mcp_server.run(transport="streamable-http", host="0.0.0.0", port=8000)
    else:
        # Default to stdio transport for standard MCP clients
        mcp_server.run(transport="stdio")

if __name__ == "__main__":
    main()