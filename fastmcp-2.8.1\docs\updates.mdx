---
title: "FastMCP Updates"
sidebarTitle: "Updates"
icon: "sparkles"
tag: "New!"
---

<Update label="FastMCP 2.8" description="June 11, 2025" tags={["Releases", "Blog Posts"]}>
<Card 
title="FastMCP 2.8: Transform and Roll Out" href="https://www.jlowin.dev/blog/fastmcp-2-8-tool-transformation" 
img="https://www.jlowin.dev/_image?href=%2F_astro%2Fhero.su3kspkP.png&w=1000&h=500&f=webp" 
cta="Read more"  
>
FastMCP 2.8 is here, and it's all about taking control of your tools.

This release is packed with new features for curating the perfect LLM experience:

🛠️ Tool Transformation

The headline feature lets you wrap any tool—from your own code, a third-party library, or an OpenAPI spec—to create an enhanced, LLM-friendly version. You can rename arguments, rewrite descriptions, and hide parameters without touching the original code.

This feature was developed in close partnership with <PERSON>. As <PERSON> brilliantly [put it](https://www.linkedin.com/posts/williamseaston_huge-thanks-to-william-easton-for-providing-activity-7338011349525983232-Mw6T?utm_source=share&utm_medium=member_desktop&rcm=ACoAAAAd6d0B3uL9zpCsq9eYWKi3HIvb8eN_r_Q), "Tool transformation flips Prompt Engineering on its head: stop writing tool-friendly LLM prompts and start providing LLM-friendly tools."

🏷️ Component Control

Now that you're transforming tools, you need a way to hide the old ones! In FastMCP 2.8 you can programmatically enable/disable any component, and for everyone who's been asking what FastMCP's tags are for—they finally have a purpose! You can now use tags to declaratively filter which components are exposed to your clients.

🚀 Pragmatic by Default

Lastly, to ensure maximum compatibility with the ecosystem, we've made the pragmatic decision to default all OpenAPI routes to Tools, making your entire API immediately accessible to any tool-using agent. When the industry catches up and supports resources, we'll restore the old default -- but no reason you should do extra work before OpenAI, Anthropic, or Google!

</Card>
</Update>

<Update label="FastMCP 2.7" description="June 6, 2025" tags={["Releases"]}>
<Card 
title="FastMCP 2.7: Pare Programming" href="https://github.com/jlowin/fastmcp/releases/tag/v2.7.0" 
img="assets/updates/release-2-7.png" 
cta="Read the release notes"  
>
FastMCP 2.7 has been released!

Most notably, it introduces the highly requested (and Pythonic) "naked" decorator usage:

```python {3}
mcp = FastMCP()

@mcp.tool
def add(a: int, b: int) -> int:
    return a + b
```

In addition, decorators now return the objects they create, instead of the decorated function. This is an important usability enhancement.

The bulk of the update is focused on improving the FastMCP internals, including a few breaking internal changes to private APIs. A number of functions that have clung on since 1.0 are now deprecated.
</Card>
</Update>



<Update label="FastMCP 2.6" description="June 2, 2025" tags={["Releases", "Blog Posts"]}>
<Card 
title="FastMCP 2.6: Blast Auth" href="https://www.jlowin.dev/blog/fastmcp-2-6" 
img="https://www.jlowin.dev/_image?href=%2F_astro%2Fhero.Bsu8afiw.png&w=1000&h=500&f=webp" 
cta="Read more"  
>
FastMCP 2.6 is here!

This release introduces first-class authentication for MCP servers and clients, including pragmatic Bearer token support and seamless OAuth 2.1 integration. This release aligns with how major AI platforms are adopting MCP today, making it easier than ever to securely connect your tools to real-world AI models. Dive into the update and secure your stack with minimal friction.
</Card>
</Update>

<Update description="May 21, 2025" label="Vibe-Testing" tags={["Blog Posts", "Tutorials"]}>
<Card
title="Stop Vibe-Testing Your MCP Server"
href="https://www.jlowin.dev/blog/stop-vibe-testing-mcp-servers"
img="https://www.jlowin.dev/_image?href=%2F_astro%2Fhero.BUPy9I9c.png&w=1000&h=500&f=webp"
cta="Read more"
>

Your tests are bad and you should feel bad.

Stop vibe-testing your MCP server through LLM guesswork. FastMCP 2.0 introduces in-memory testing for fast, deterministic, and fully Pythonic validation of your MCP logic—no network, no subprocesses, no vibes.

</Card>
</Update>


<Update description="May 8, 2025" label="10,000 Stars" tags={["Blog Posts"]}>
<Card
title="Reflecting on FastMCP at 10k stars 🌟"
href="https://www.jlowin.dev/blog/fastmcp-2-10k-stars"
img="https://www.jlowin.dev/_image?href=%2F_astro%2Fhero.Cnvci9Q_.png&w=1000&h=500&f=webp"
cta="Read more"
>

In just six weeks since its relaunch, FastMCP has surpassed 10,000 GitHub stars—becoming the fastest-growing OSS project in our orbit. What started as a personal itch has become the backbone of Python-based MCP servers, powering a rapidly expanding ecosystem. While the protocol itself evolves, FastMCP continues to lead with clarity, developer experience, and opinionated tooling. Here’s to what’s next.

</Card>
</Update>

<Update description="May 8, 2025" label="FastMCP 2.3" tags={["Blog Posts", "Releases"]}>
<Card
title="Now Streaming: FastMCP 2.3"
href="https://www.jlowin.dev/blog/fastmcp-2-3-streamable-http"
img="https://www.jlowin.dev/_image?href=%2F_astro%2Fhero.M_hv6gEB.png&w=1000&h=500&f=webp"
cta="Read more"
>

FastMCP 2.3 introduces full support for Streamable HTTP, a modern alternative to SSE that simplifies MCP deployments over the web. It’s efficient, reliable, and now the default HTTP transport. Just run your server with transport="streamable-http" and connect clients via a standard URL—FastMCP handles the rest. No special setup required. This release makes deploying MCP servers easier and more portable than ever.

</Card>
</Update>

<Update description="April 23, 2025" label="Proxy Servers" tags={["Blog Posts", "Tutorials"]}>
<Card
title="MCP Proxy Servers with FastMCP 2.0"
href="https://www.jlowin.dev/blog/fastmcp-proxy"
img="https://www.jlowin.dev/_image?href=%2F_astro%2Frobot-hero.DpmAqgui.png&w=1000&h=500&f=webp"
cta="Read more"
>

Even AI needs a good travel adapter 🔌


FastMCP now supports proxying arbitrary MCP servers, letting you run a local FastMCP instance that transparently forwards requests to any remote or third-party server—regardless of transport. This enables transport bridging (e.g., stdio ⇄ SSE), simplified client configuration, and powerful gateway patterns. Proxies are fully composable with other FastMCP servers, letting you mount or import them just like local servers. Use `FastMCP.from_client()` to wrap any backend in a clean, Pythonic proxy.
</Card>
</Update>

<Update label="FastMCP 2.0" description="April 16, 2025" tags={["Releases", "Blog Posts"]}>
<Card
title="Introducing FastMCP 2.0 🚀"
href="https://www.jlowin.dev/blog/fastmcp-2"
img="https://www.jlowin.dev/_image?href=%2F_astro%2Fhero.DpbmGNrr.png&w=1000&h=500&f=webp"
cta="Read more"
>

This major release reimagines FastMCP as a full ecosystem platform, with powerful new features for composition, integration, and client interaction. You can now compose local and remote servers, proxy arbitrary MCP servers (with transport translation), and generate MCP servers from OpenAPI or FastAPI apps. A new client infrastructure supports advanced workflows like LLM sampling. 

FastMCP 2.0 builds on the success of v1 with a cleaner, more flexible foundation—try it out today!
</Card>
</Update>



<Update label="Official SDK" description="December 3, 2024" tags={["Announcements"]}>
<Card
title="FastMCP is joining the official MCP Python SDK!"
href="https://bsky.app/profile/jlowin.dev/post/3lch4xk5cf22c"
icon="sparkles"
cta="Read the announcement"
>
FastMCP 1.0 will become part of the official MCP Python SDK!
</Card>
</Update>



<Update label="FastMCP 1.0" description="December 1, 2024" tags={["Releases", "Blog Posts"]}>
<Card
title="Introducing FastMCP 🚀"
href="https://www.jlowin.dev/blog/introducing-fastmcp"
img="https://www.jlowin.dev/_image?href=%2F_astro%2Ffastmcp.Bep7YlTw.png&w=1000&h=500&f=webp"
cta="Read more"
>
Because life's too short for boilerplate.

This is where it all started. FastMCP’s launch post introduced a clean, Pythonic way to build MCP servers without the protocol overhead. Just write functions; FastMCP handles the rest. What began as a weekend project quickly became the foundation of a growing ecosystem.
</Card>
</Update>

