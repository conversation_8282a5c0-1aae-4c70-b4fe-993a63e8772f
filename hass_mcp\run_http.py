#!/usr/bin/env python
"""
Streamable HTTP entry point for running Hass-MCP with modern HTTP transport.
This script demonstrates how to run the server with streamable-http transport.
"""

import os
import logging
from .server import mcp_server

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """
    Run the Hass-MCP server with streamable-http transport.
    This is the modern alternative to SSE with better performance.
    """
    # Configuration from environment variables
    host = os.environ.get("MCP_HOST", "0.0.0.0")
    port = int(os.environ.get("MCP_PORT", "8000"))
    
    logger.info(f"Starting Hass-MCP server with streamable-http transport on {host}:{port}")
    
    # Run with streamable-http transport using FastMCP 2.8.1 built-in support
    mcp_server.run(
        transport="streamable-http",
        host=host,
        port=port
    )

if __name__ == "__main__":
    main()
