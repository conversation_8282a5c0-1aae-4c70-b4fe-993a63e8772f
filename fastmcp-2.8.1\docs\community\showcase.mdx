---
title: 'Community Showcase'
description: 'High-quality projects and examples from the FastMCP community'
icon: 'users'
---

import { YouTubeEmbed } from '/snippets/youtube-embed.mdx'

## Featured Projects

Discover exemplary MCP servers and implementations created by our community. These projects demonstrate best practices and innovative uses of FastMCP.

### Learning Resources

<Card title="MCP Dummy Server" icon="graduation-cap" href="https://github.com/WaiYanNyeinNaing/mcp-dummy-server">
  A comprehensive educational example demonstrating FastMCP best practices with professional dual-transport server implementation, interactive test client, and detailed documentation.
</Card>

#### Video Tutorials

**Build Remote MCP Servers w/ Python & FastMCP** - Claude Integrations Tutorial by Greg + Code

<YouTubeEmbed 
  videoId="bOYkbXP-GGo" 
  title="Build Remote MCP Servers w/ Python & FastMCP" 
/>

**FastMCP — the best way to build an MCP server with Python** - Tutorial by ZazenCodes

<YouTubeEmbed 
  videoId="rnljvmHorQw" 
  title="FastMCP — the best way to build an MCP server with Python" 
/>

**Speedrun a MCP server for <PERSON> (fastmcp)** - Tutorial by <PERSON> from Prefect

<YouTubeEmbed 
  videoId="67ZwpkUEtSI" 
  title="Speedrun a MCP server for Claude Desktop (fastmcp)" 
/>

### Community Examples

Have you built something interesting with FastMCP? We'd love to feature high-quality examples here! Start a [discussion on GitHub](https://github.com/jlowin/fastmcp/discussions) to share your project.

## Contributing

To get your project featured:

1. Ensure your project demonstrates best practices
2. Include comprehensive documentation
3. Add clear usage examples
4. Open a discussion in our [GitHub Discussions](https://github.com/jlowin/fastmcp/discussions)

We review submissions regularly and feature projects that provide value to the FastMCP community.

## Further Reading

- [Contrib Modules](/integrations/contrib) - Community-contributed modules that are distributed with FastMCP itself