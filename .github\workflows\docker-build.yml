name: ci

on:
  push: {}

env:
  DOCKER_IMAGE: voska/hass-mcp
  PLATFORMS: linux/amd64,linux/arm64

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_IMAGE }}
          tags: |
            # Always include git sha for immutable references
            type=sha,format=long
            # Set latest tag for default branch
            type=raw,value=latest,enable={{is_default_branch}}
            # Tag branch builds (e.g. master)
            type=ref,event=branch
            # Full version numbers for exact versions
            type=semver,pattern={{version}}
            # Major.minor for API compatibility
            type=semver,pattern={{major}}.{{minor}}
            # Major only for major version compatibility
            type=semver,pattern={{major}},enable=${{ !startsWith(github.ref, 'refs/tags/v0.') }}

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: ${{ env.PLATFORMS }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
